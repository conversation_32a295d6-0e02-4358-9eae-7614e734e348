package benchmark

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
)

type BenchmarkResultExporter struct {
	prometheusClient v1.API
	grafanaURL       string
	grafanaAPIKey    string
	httpClient       *http.Client
}

func NewBenchmarkResultExporter(prometheusURL, grafanaURL, grafanaAPIKey string) (*BenchmarkResultExporter, error) {
	client, err := api.NewClient(api.Config{
		Address: prometheusURL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Prometheus client: %w", err)
	}

	return &BenchmarkResultExporter{
		prometheusClient: v1.NewAPI(client),
		grafanaURL:       grafanaURL,
		grafanaAPIKey:    grafanaAPIKey,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

func (bre *BenchmarkResultExporter) ExportResults(results []BenchmarkResult) error {
	for _, result := range results {
		if err := bre.exportToPrometheus(result); err != nil {
			return fmt.Errorf("failed to export to Prometheus: %w", err)
		}

		if err := bre.exportToGrafana(result); err != nil {
			return fmt.Errorf("failed to export to Grafana: %w", err)
		}
	}

	return nil
}

func (bre *BenchmarkResultExporter) exportToPrometheus(result BenchmarkResult) error {
	metrics := []PrometheusMetric{
		{
			Name:      "benchmark_operations_total",
			Value:     float64(result.TotalOps),
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service": result.ServiceName,
				"test":    result.TestName,
				"status":  "success",
			},
		},
		{
			Name:      "benchmark_duration_seconds",
			Value:     result.Duration.Seconds(),
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service": result.ServiceName,
				"test":    result.TestName,
			},
		},
		{
			Name:      "benchmark_ops_per_second",
			Value:     result.OpsPerSecond,
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service": result.ServiceName,
				"test":    result.TestName,
			},
		},
		{
			Name:      "benchmark_latency_seconds",
			Value:     result.AvgLatency.Seconds(),
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service":    result.ServiceName,
				"test":       result.TestName,
				"percentile": "avg",
			},
		},
		{
			Name:      "benchmark_latency_seconds",
			Value:     result.P95Latency.Seconds(),
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service":    result.ServiceName,
				"test":       result.TestName,
				"percentile": "p95",
			},
		},
		{
			Name:      "benchmark_latency_seconds",
			Value:     result.P99Latency.Seconds(),
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service":    result.ServiceName,
				"test":       result.TestName,
				"percentile": "p99",
			},
		},
		{
			Name:      "benchmark_error_rate_percent",
			Value:     result.ErrorRate,
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service": result.ServiceName,
				"test":    result.TestName,
			},
		},
	}

	if result.MemoryUsage > 0 {
		metrics = append(metrics, PrometheusMetric{
			Name:      "benchmark_memory_usage_bytes",
			Value:     float64(result.MemoryUsage),
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service": result.ServiceName,
				"test":    result.TestName,
			},
		})
	}

	if result.CPUUsage > 0 {
		metrics = append(metrics, PrometheusMetric{
			Name:      "benchmark_cpu_usage_percent",
			Value:     result.CPUUsage,
			Timestamp: result.Timestamp,
			Labels: map[string]string{
				"service": result.ServiceName,
				"test":    result.TestName,
			},
		})
	}

	fmt.Printf("Exporting %d metrics to Prometheus for %s/%s\n",
		len(metrics), result.ServiceName, result.TestName)

	return nil
}

func (bre *BenchmarkResultExporter) exportToGrafana(result BenchmarkResult) error {
	annotation := GrafanaAnnotation{
		Time:    result.Timestamp.UnixNano() / int64(time.Millisecond),
		TimeEnd: result.Timestamp.Add(result.Duration).UnixNano() / int64(time.Millisecond),
		Tags:    []string{"benchmark", result.ServiceName, result.TestName},
		Text: fmt.Sprintf("Benchmark: %s/%s\nOps/sec: %.2f\nAvg Latency: %v\nP95 Latency: %v\nError Rate: %.2f%%",
			result.ServiceName, result.TestName, result.OpsPerSecond,
			result.AvgLatency, result.P95Latency, result.ErrorRate),
	}

	return bre.createGrafanaAnnotation(annotation)
}

func (bre *BenchmarkResultExporter) createGrafanaAnnotation(annotation GrafanaAnnotation) error {
	jsonData, err := json.Marshal(annotation)
	if err != nil {
		return fmt.Errorf("failed to marshal annotation: %w", err)
	}

	req, err := http.NewRequest("POST", bre.grafanaURL+"/api/annotations", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+bre.grafanaAPIKey)

	resp, err := bre.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("grafana API error: %d", resp.StatusCode)
	}

	return nil
}

func (bre *BenchmarkResultExporter) QueryHistoricalResults(ctx context.Context, service, test string, duration time.Duration) ([]BenchmarkResult, error) {
	query := fmt.Sprintf(`benchmark_ops_per_second{service="%s",test="%s"}`, service, test)

	result, warnings, err := bre.prometheusClient.QueryRange(ctx, query, v1.Range{
		Start: time.Now().Add(-duration),
		End:   time.Now(),
		Step:  time.Minute * 5,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to query Prometheus: %w", err)
	}

	if len(warnings) > 0 {
		fmt.Printf("Prometheus query warnings: %v\n", warnings)
	}

	var results []BenchmarkResult

	if matrix, ok := result.(model.Matrix); ok {
		for _, sample := range matrix {
			for _, value := range sample.Values {
				results = append(results, BenchmarkResult{
					ServiceName:  service,
					TestName:     test,
					OpsPerSecond: float64(value.Value),
					Timestamp:    value.Timestamp.Time(),
				})
			}
		}
	}

	return results, nil
}

func (bre *BenchmarkResultExporter) CompareResults(ctx context.Context, current BenchmarkResult, historicalDuration time.Duration) (*PerformanceComparison, error) {
	historical, err := bre.QueryHistoricalResults(ctx, current.ServiceName, current.TestName, historicalDuration)
	if err != nil {
		return nil, fmt.Errorf("failed to query historical results: %w", err)
	}

	if len(historical) == 0 {
		return &PerformanceComparison{
			Current:      current,
			Historical:   nil,
			IsRegression: false,
			Message:      "No historical data available for comparison",
		}, nil
	}

	var avgOpsPerSec float64
	for _, result := range historical {
		avgOpsPerSec += result.OpsPerSecond
	}
	avgOpsPerSec /= float64(len(historical))

	regressionThreshold := 0.8
	isRegression := current.OpsPerSecond < (avgOpsPerSec * regressionThreshold)

	comparison := &PerformanceComparison{
		Current:       current,
		Historical:    &historical[len(historical)-1], // Most recent historical result
		IsRegression:  isRegression,
		ChangePercent: ((current.OpsPerSecond - avgOpsPerSec) / avgOpsPerSec) * 100,
	}

	if isRegression {
		comparison.Message = fmt.Sprintf("Performance regression detected: %.2f%% slower than historical average",
			-comparison.ChangePercent)
	} else {
		comparison.Message = fmt.Sprintf("Performance improved: %.2f%% faster than historical average",
			comparison.ChangePercent)
	}

	return comparison, nil
}

type PrometheusMetric struct {
	Name      string            `json:"name"`
	Value     float64           `json:"value"`
	Timestamp time.Time         `json:"timestamp"`
	Labels    map[string]string `json:"labels"`
}

type GrafanaAnnotation struct {
	Time    int64    `json:"time"`
	TimeEnd int64    `json:"timeEnd"`
	Tags    []string `json:"tags"`
	Text    string   `json:"text"`
}

type PerformanceComparison struct {
	Current       BenchmarkResult  `json:"current"`
	Historical    *BenchmarkResult `json:"historical,omitempty"`
	IsRegression  bool             `json:"is_regression"`
	ChangePercent float64          `json:"change_percent"`
	Message       string           `json:"message"`
}

type BenchmarkReportGenerator struct {
	exporter *BenchmarkResultExporter
}

func NewBenchmarkReportGenerator(exporter *BenchmarkResultExporter) *BenchmarkReportGenerator {
	return &BenchmarkReportGenerator{
		exporter: exporter,
	}
}

func (brg *BenchmarkReportGenerator) GenerateReport(ctx context.Context, results []BenchmarkResult) (*BenchmarkReport, error) {
	report := &BenchmarkReport{
		GeneratedAt: time.Now(),
		Results:     results,
		Comparisons: make([]PerformanceComparison, 0),
		Summary:     BenchmarkSummary{},
	}

	for _, result := range results {
		comparison, err := brg.exporter.CompareResults(ctx, result, 24*time.Hour)
		if err != nil {
			fmt.Printf("Failed to compare results for %s/%s: %v\n", result.ServiceName, result.TestName, err)
			continue
		}
		report.Comparisons = append(report.Comparisons, *comparison)
	}

	report.Summary = brg.generateSummary(results, report.Comparisons)

	return report, nil
}

func (brg *BenchmarkReportGenerator) generateSummary(results []BenchmarkResult, comparisons []PerformanceComparison) BenchmarkSummary {
	summary := BenchmarkSummary{
		TotalTests:   len(results),
		Regressions:  0,
		Improvements: 0,
		AvgOpsPerSec: 0,
		AvgLatency:   0,
		AvgErrorRate: 0,
	}

	var totalOps, totalLatency, totalErrorRate float64

	for _, result := range results {
		totalOps += result.OpsPerSecond
		totalLatency += result.AvgLatency.Seconds()
		totalErrorRate += result.ErrorRate
	}

	if len(results) > 0 {
		summary.AvgOpsPerSec = totalOps / float64(len(results))
		summary.AvgLatency = totalLatency / float64(len(results))
		summary.AvgErrorRate = totalErrorRate / float64(len(results))
	}

	for _, comparison := range comparisons {
		if comparison.IsRegression {
			summary.Regressions++
		} else if comparison.ChangePercent > 0 {
			summary.Improvements++
		}
	}

	return summary
}

type BenchmarkReport struct {
	GeneratedAt time.Time               `json:"generated_at"`
	Results     []BenchmarkResult       `json:"results"`
	Comparisons []PerformanceComparison `json:"comparisons"`
	Summary     BenchmarkSummary        `json:"summary"`
}

type BenchmarkSummary struct {
	TotalTests   int     `json:"total_tests"`
	Regressions  int     `json:"regressions"`
	Improvements int     `json:"improvements"`
	AvgOpsPerSec float64 `json:"avg_ops_per_sec"`
	AvgLatency   float64 `json:"avg_latency_seconds"`
	AvgErrorRate float64 `json:"avg_error_rate_percent"`
}
