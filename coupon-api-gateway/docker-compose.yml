services:
  # jaeger:
  #   image: jaegertracing/all-in-one:1.56
  #   container_name: jaeger
  #   restart: unless-stopped
  #   ports:
  #     - "16686:16686"
  #     - "6831:6831/udp"
  #   networks:
  #     - coupon-network

  api-gateway:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    image: registry-gitlab.zalopay.vn/phunn4/coupon-api-gateway
    container_name: api-gateway
    # depends_on:
    #   - jaeger
    env_file:
      - .env
    ports:
      - "8080:8080"
    restart: unless-stopped
    networks:
      - coupon-network

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
