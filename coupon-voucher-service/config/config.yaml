service:
  name: "voucher-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "${VOUCHER_SERVICE_CLIENT_ID}"
  client_key: "${VOUCHER_SERVICE_CLIENT_KEY}"

database:
  host: "postgres-voucher"
  port: 5432
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  name: "${POSTGRES_DB}"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  max_lifetime: "1h"

redis:
  host: "redis-voucher"
  port: 6379
  password: "${REDIS_PASSWORD}"
  db: 0
  pool_size: 10
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

downstream_services:
  auth_service_addr: "auth-service:50051"
  user_service_addr: "user-service:50051"
  order_service_addr: "order-service:50051"

kafka:
  brokers: ["kafka:29092"]
  group_id: "voucher-service"
  topics:
    voucher_events: "voucher-events"

jaeger:
  host: "jaeger"
  port: 6831

logging:
  level: "debug"
  format: "json"

metrics:
  path: "/metrics"

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"
