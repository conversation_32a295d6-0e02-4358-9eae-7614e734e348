#!/bin/bash

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MONITORING_DIR="$PROJECT_ROOT"
NETWORK_NAME="coupon-network"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

create_network() {
    print_status "Creating Docker network: $NETWORK_NAME"
    
    if docker network ls | grep -q "$NETWORK_NAME"; then
        print_warning "Network $NETWORK_NAME already exists"
    else
        docker network create "$NETWORK_NAME"
        print_success "Network $NETWORK_NAME created"
    fi
}

create_directories() {
    print_status "Creating monitoring directories..."
    
    mkdir -p "$MONITORING_DIR/prometheus/data"
    mkdir -p "$MONITORING_DIR/grafana/data"
    mkdir -p "$MONITORING_DIR/alertmanager/data"
    
    sudo chown -R 472:472 "$MONITORING_DIR/grafana/data" 2>/dev/null || true
    
    print_success "Monitoring directories created"
}

validate_configs() {
    print_status "Validating configuration files..."
    
    if [ ! -f "$MONITORING_DIR/prometheus/prometheus.yml" ]; then
        print_error "Prometheus configuration file not found: $MONITORING_DIR/prometheus/prometheus.yml"
        exit 1
    fi
    
    if [ ! -f "$MONITORING_DIR/alertmanager/alertmanager.yml" ]; then
        print_error "AlertManager configuration file not found: $MONITORING_DIR/alertmanager/alertmanager.yml"
        exit 1
    fi
    
    if [ ! -d "$MONITORING_DIR/grafana/provisioning" ]; then
        print_error "Grafana provisioning directory not found: $MONITORING_DIR/grafana/provisioning"
        exit 1
    fi
    
    print_success "Configuration files validated"
}

start_monitoring() {
    print_status "Starting monitoring stack..."
    
    cd "$PROJECT_ROOT"
    
    docker-compose up -d
    
    print_success "Monitoring stack started"
}

wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    print_status "Waiting for Prometheus..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:9090/-/ready >/dev/null 2>&1; then
            print_success "Prometheus is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Prometheus failed to start within timeout"
        exit 1
    fi
    
    print_status "Waiting for Grafana..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:3000/api/health >/dev/null 2>&1; then
            print_success "Grafana is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Grafana failed to start within timeout"
        exit 1
    fi
    
    print_status "Waiting for AlertManager..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:9093/-/ready >/dev/null 2>&1; then
            print_success "AlertManager is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "AlertManager failed to start within timeout"
        exit 1
    fi
}

display_urls() {
    print_success "Monitoring stack deployed successfully!"
    echo ""
    echo "Service URLs:"
    echo "  Grafana:      http://localhost:3000 (admin/admin123)"
    echo "  Prometheus:   http://localhost:9090"
    echo "  AlertManager: http://localhost:9093"
    echo "  Node Exporter: http://localhost:9100"
    echo "  cAdvisor:     http://localhost:8080"
    echo ""
    echo "Default Grafana credentials:"
    echo "  Username: admin"
    echo "  Password: admin123"
    echo ""
    echo "To view logs: docker-compose logs -f [service]"
    echo "To stop: docker-compose down"
}

run_health_checks() {
    print_status "Running health checks..."
    
    print_status "Checking Prometheus targets..."
    targets_response=$(curl -s http://localhost:9090/api/v1/targets)
    if echo "$targets_response" | grep -q '"health":"up"'; then
        print_success "Some Prometheus targets are healthy"
    else
        print_warning "No healthy Prometheus targets found. Services may not be running."
    fi
    
    print_status "Checking Grafana datasources..."
    if curl -s -u admin:admin123 http://localhost:3000/api/datasources | grep -q "Prometheus"; then
        print_success "Grafana Prometheus datasource configured"
    else
        print_warning "Grafana Prometheus datasource not found"
    fi
}

main() {
    echo "=================================================="
    echo "  Coupon System - Monitoring Deployment Script"
    echo "=================================================="
    echo ""
    
    create_network
    create_directories
    validate_configs
    start_monitoring
    wait_for_services
    run_health_checks
    display_urls
    
    echo ""
    print_success "Deployment completed successfully!"
}

case "${1:-}" in
    "start")
        start_monitoring
        wait_for_services
        display_urls
        ;;
    "stop")
        print_status "Stopping monitoring stack..."
        cd "$PROJECT_ROOT"
        docker-compose down
        print_success "Monitoring stack stopped"
        ;;
    "restart")
        print_status "Restarting monitoring stack..."
        cd "$PROJECT_ROOT"
        docker-compose down
        docker-compose up -d
        wait_for_services
        display_urls
        ;;
    "status")
        print_status "Checking monitoring stack status..."
        cd "$PROJECT_ROOT"
        docker-compose ps
        ;;
    "logs")
        cd "$PROJECT_ROOT"
        docker-compose logs -f "${2:-}"
        ;;
    "health")
        run_health_checks
        ;;
     "help")
        echo "Usage: $0 [start|stop|restart|status|logs|health|help]"
        echo ""
        echo "Coupon System - Monitoring Deployment Script"
        echo "============================================="
        echo ""
        echo "This script manages the monitoring stack deployment including:"
        echo "- Prometheus (metrics collection)"
        echo "- Grafana (visualization dashboards)"
        echo "- AlertManager (alerting)"
        echo "- Node Exporter (system metrics)"
        echo "- cAdvisor (container metrics)"
        echo ""
        echo "Commands:"
        echo "  (no args)  Deploy complete monitoring stack (default)"
        echo "  start      Start monitoring services"
        echo "  stop       Stop monitoring services"
        echo "  restart    Restart monitoring services"
        echo "  status     Show monitoring services status"
        echo "  logs       Show monitoring services logs"
        echo "             Use: $0 logs [service-name] for specific service"
        echo "  health     Run health checks on monitoring services"
        echo "  help       Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    # Deploy complete stack"
        echo "  $0 start              # Start services"
        echo "  $0 logs prometheus    # Show Prometheus logs"
        echo "  $0 health             # Check service health"
        echo ""
        echo "Service URLs (after deployment):"
        echo "  Grafana:      http://localhost:3000 (admin/admin123)"
        echo "  Prometheus:   http://localhost:9090"
        echo "  AlertManager: http://localhost:9093"
        echo "  Node Exporter: http://localhost:9100"
        echo "  cAdvisor:     http://localhost:8080"
        echo ""
        echo "Prerequisites:"
        echo "  - Docker and Docker Compose installed"
        echo "  - Docker daemon running"
        echo ""
        echo "For more information, see the monitoring documentation."
        ;;
    *)
        main
        ;;
esac

