.PHONY: help deploy monitoring-start monitoring-stop monitoring-restart monitoring-status monitoring-logs monitoring-clean benchmark-all benchmark-user benchmark-voucher benchmark-product benchmark-auth benchmark-api-gateway benchmark-order benchmark-notification benchmark-report benchmark-clean benchmark-gitlab benchmark-branch ********************** monitoring-backup monitoring-restore

TIMESTAMP := $(shell date +%Y%m%d_%H%M%S)
RESULTS_DIR := benchmark-results
BACKUP_DIR := monitoring-backups
SERVICES := user-service voucher-service product-service auth-service api-gateway order-service notification-service

deploy:
	@echo "Deploying monitoring stack..."
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh

monitoring-start:
	@echo "Starting monitoring services..."
	@./scripts/deploy.sh start

monitoring-stop:
	@echo "Stopping monitoring services..."
	@./scripts/deploy.sh stop

monitoring-restart:
	@echo "Restarting monitoring services..."
	@./scripts/deploy.sh restart

monitoring-status:
	@echo "Monitoring services status:"
	@./scripts/deploy.sh status

monitoring-logs:
	@echo "Showing monitoring logs..."
	@./scripts/deploy.sh logs $(SERVICE)

monitoring-health:
	@echo "Running monitoring health checks..."
	@./scripts/deploy.sh health

monitoring-clean:
	@echo "Cleaning up monitoring stack..."
	@docker-compose down -v
	@docker system prune -f
	@echo "Monitoring cleanup completed"

benchmark-all:
	@echo "Running benchmarks for all services..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh
	@echo "All benchmarks completed. Results in $(RESULTS_DIR)/"

benchmark-user:
	@echo "Running user service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@USER_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "User service benchmarks completed"

benchmark-voucher:
	@echo "Running voucher service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@VOUCHER_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Voucher service benchmarks completed"

benchmark-product:
	@echo "Running product service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@PRODUCT_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Product service benchmarks completed"

benchmark-auth:
	@echo "Running auth service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@AUTH_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Auth service benchmarks completed"

benchmark-api-gateway:
	@echo "Running API gateway benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@API_GATEWAY_ONLY=true ./scripts/run-benchmarks.sh
	@echo "API gateway benchmarks completed"

benchmark-order:
	@echo "Running order service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@ORDER_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Order service benchmarks completed"

benchmark-notification:
	@echo "Running notification service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@NOTIFICATION_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Notification service benchmarks completed"

benchmark-load:
	@echo "Running load testing scenarios..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh load
	@echo "Load testing completed"

benchmark-stress:
	@echo "Running stress testing scenarios..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh stress
	@echo "Stress testing completed"

benchmark-report:
	@echo "Generating benchmark report..."
	@mkdir -p $(RESULTS_DIR)
	@echo "# Benchmark Report - $(TIMESTAMP)" > $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "## Summary" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "Generated: $(shell date)" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@for file in $(RESULTS_DIR)/*_$(TIMESTAMP).txt; do \
		if [ -f "$$file" ]; then \
			echo "### $$(basename $$file .txt)" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo '```' >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			tail -20 "$$file" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo '```' >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
		fi \
	done
	@echo "Benchmark report generated: $(RESULTS_DIR)/report_$(TIMESTAMP).md"

benchmark-clean:
	@echo "Cleaning up benchmark results..."
	@rm -rf $(RESULTS_DIR)
	@echo "Benchmark results cleaned"

monitoring-backup:
	@echo "Backing up monitoring data..."
	@mkdir -p $(BACKUP_DIR)
	@docker run --rm -v prometheus-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/prometheus-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v grafana-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/grafana-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v alertmanager-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/alertmanager-$(TIMESTAMP).tar.gz /data
	@echo "Monitoring data backed up to $(BACKUP_DIR)/"

monitoring-restore:
	@echo "Restoring monitoring data..."
	@if [ -z "$(BACKUP_DATE)" ]; then \
		echo "Usage: make monitoring-restore BACKUP_DATE=20231201_120000"; \
		exit 1; \
	fi
	@./scripts/deploy.sh stop
	@docker run --rm -v prometheus-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/prometheus-$(BACKUP_DATE).tar.gz -C /
	@docker run --rm -v grafana-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/grafana-$(BACKUP_DATE).tar.gz -C /
	@docker run --rm -v alertmanager-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/alertmanager-$(BACKUP_DATE).tar.gz -C /
	@./scripts/deploy.sh start
	@echo "Monitoring data restored from $(BACKUP_DATE)"

urls:
	@echo "Monitoring Service URLs:"
	@echo "  Grafana:      http://localhost:3000 (admin/admin123)"
	@echo "  Prometheus:   http://localhost:9090"
	@echo "  AlertManager: http://localhost:9093"
	@echo "  Node Exporter: http://localhost:9100"
	@echo "  cAdvisor:     http://localhost:8080"

dashboard-import:
	@echo "Importing Grafana dashboards..."
	@for dashboard in grafana/dashboards/*.json; do \
		echo "Importing $$(basename $$dashboard)..."; \
		curl -X POST \
			-H "Content-Type: application/json" \
			-u admin:admin123 \
			-d @$$dashboard \
			http://localhost:3000/api/dashboards/db; \
		echo ""; \
	done
	@echo "Dashboard import completed"

metrics-test:
	@echo "Testing metrics endpoints..."
	@for service in $(SERVICES); do \
		echo "Testing coupon-$$service metrics..."; \
		curl -s http://localhost:2112/metrics | head -5 || echo "Service not available"; \
		echo ""; \
	done

ci-benchmark:
	@echo "Running CI benchmark suite..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh quick
	@echo "CI benchmarks completed"

clean-all: monitoring-clean benchmark-clean
	@echo "Cleaning up all monitoring and benchmark data..."
	@rm -rf $(BACKUP_DIR)
	@echo "Complete cleanup finished"

