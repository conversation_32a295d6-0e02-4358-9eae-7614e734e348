{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1.2}, {"color": "red", "value": 1.5}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(benchmark_duration_seconds_bucket[5m])) by (service, test, le)) / histogram_quantile(0.95, sum(rate(benchmark_duration_seconds_bucket[1h] offset 24h)) by (service, test, le))", "interval": "", "legendFormat": "{{service}} - {{test}} (vs 24h ago)", "refId": "A"}], "title": "Performance Regression - Duration (Current vs 24h ago)", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.8}, {"color": "red", "value": 0.6}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(rate(benchmark_operations_total[5m])) by (service, test) / sum(rate(benchmark_operations_total[1h] offset 24h)) by (service, test)", "interval": "", "legendFormat": "{{service}} - {{test}} (vs 24h ago)", "refId": "A"}], "title": "Throughput Regression - Ops/sec (Current vs 24h ago)", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1.2}, {"color": "red", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "benchmark_memory_usage_bytes / (benchmark_memory_usage_bytes offset 24h)", "interval": "", "legendFormat": "{{service}} - {{test}} (vs 24h ago)", "refId": "A"}], "title": "Memory Usage Regression (Current vs 24h ago)", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 5}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "(sum(rate(benchmark_errors_total[5m])) by (service, test) / sum(rate(benchmark_operations_total[5m])) by (service, test)) / (sum(rate(benchmark_errors_total[1h] offset 24h)) by (service, test) / sum(rate(benchmark_operations_total[1h] offset 24h)) by (service, test))", "interval": "", "legendFormat": "{{service}} - {{test}} (vs 24h ago)", "refId": "A"}], "title": "Error Rate Regression (Current vs 24h ago)", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1.2}, {"color": "red", "value": 1.5}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Regression Factor"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "id": 5, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Regression Factor"}]}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(benchmark_duration_seconds_bucket[5m])) by (service, test, le)) / histogram_quantile(0.95, sum(rate(benchmark_duration_seconds_bucket[1h] offset 24h)) by (service, test, le))", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Performance Regression Summary", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "le": true}, "indexByName": {}, "renameByName": {"Value": "Regression Factor", "service": "Service", "test": "Test"}}}], "type": "table"}], "schemaVersion": 27, "style": "dark", "tags": ["coupon-system", "benchmarks", "regression"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Performance Regression Analysis", "uid": "performance-regression", "version": 1}