global:
  smtp_smarthost: "localhost:587"
  smtp_from: "<EMAIL>"
  smtp_auth_username: "<EMAIL>"
  smtp_auth_password: "password"

route:
  group_by: ["alertname", "cluster", "service"]
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: "web.hook"
  routes:
    - match:
        severity: critical
      receiver: "critical-alerts"
    - match:
        severity: warning
      receiver: "warning-alerts"
    - match:
        alertname: ServiceDown
      receiver: "service-down-alerts"

receivers:
  - name: "web.hook"
    webhook_configs:
      - url: "http://localhost:5001/webhook"
        send_resolved: true

  - name: "critical-alerts"
    email_configs:
      - to: "<EMAIL>"
        from: "<EMAIL>"
        subject: "CRITICAL: {{ .GroupLabels.alertname }} in {{ .GroupLabels.service }}"
        html: |
          <h2>Critical Alert</h2>
          {{ range .Alerts }}
          <p><strong>Alert:</strong> {{ .Annotations.summary }}</p>
          <p><strong>Description:</strong> {{ .Annotations.description }}</p>
          <p><strong>Service:</strong> {{ .Labels.service }}</p>
          <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
          <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
          <hr>
          {{ end }}
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          {{ end }}
    webhook_configs:
      - url: "http://localhost:5001/critical"
        send_resolved: true

  - name: "warning-alerts"
    email_configs:
      - to: "<EMAIL>"
        from: "<EMAIL>"
        subject: "WARNING: {{ .GroupLabels.alertname }} in {{ .GroupLabels.service }}"
        html: |
          <h2>Warning Alert</h2>
          {{ range .Alerts }}
          <p><strong>Alert:</strong> {{ .Annotations.summary }}</p>
          <p><strong>Description:</strong> {{ .Annotations.description }}</p>
          <p><strong>Service:</strong> {{ .Labels.service }}</p>
          <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
          <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
          <hr>
          {{ end }}
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          {{ end }}

  - name: "service-down-alerts"
    email_configs:
      - to: "<EMAIL>,<EMAIL>"
        from: "<EMAIL>"
        subject: "SERVICE DOWN: {{ .GroupLabels.service }}"
        html: |
          <h2>🚨 SERVICE DOWN ALERT 🚨</h2>
          {{ range .Alerts }}
          <p><strong>URGENT:</strong> Service {{ .Labels.service }} is DOWN!</p>
          <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
          <p><strong>Started:</strong> {{ .StartsAt }}</p>
          <hr>
          {{ end }}
        text: |
          {{ range .Alerts }}
          URGENT: Service {{ .Labels.service }} is DOWN!
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt }}
          {{ end }}
    webhook_configs:
      - url: "http://localhost:5001/service-down"
        send_resolved: true

inhibit_rules:
  - source_match:
      severity: "critical"
    target_match:
      severity: "warning"
    equal: ["alertname", "service", "instance"]
