global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: "coupon-microservices"
    environment: "development"

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "node-exporter"
    static_configs:
      - targets: ["node-exporter:9100"]

  - job_name: "cadvisor"
    static_configs:
      - targets: ["cadvisor:8080"]

  - job_name: "api-gateway"
    static_configs:
      - targets: ["api-gateway:2112"]
    metrics_path: "/metrics"
    scrape_interval: 10s
    scrape_timeout: 5s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "api-gateway"
      - target_label: service
        replacement: "api-gateway"

  - job_name: "auth-service"
    static_configs:
      - targets: ["auth-service:2112"]
    metrics_path: "/metrics"
    scrape_interval: 10s
    scrape_timeout: 5s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "auth-service"
      - target_label: service
        replacement: "auth-service"

  - job_name: "user-service"
    static_configs:
      - targets: ["user-service:2112"]
    metrics_path: "/metrics"
    scrape_interval: 10s
    scrape_timeout: 5s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "user-service"
      - target_label: service
        replacement: "user-service"

  - job_name: "voucher-service"
    static_configs:
      - targets: ["voucher-service:2112"]
    metrics_path: "/metrics"
    scrape_interval: 10s
    scrape_timeout: 5s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "voucher-service"
      - target_label: service
        replacement: "voucher-service"

  - job_name: "product-service"
    static_configs:
      - targets: ["product-service:2112"]
    metrics_path: "/metrics"
    scrape_interval: 10s
    scrape_timeout: 5s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "product-service"
      - target_label: service
        replacement: "product-service"

  - job_name: "order-service"
    static_configs:
      - targets: ["order-service:2112"]
    metrics_path: "/metrics"
    scrape_interval: 10s
    scrape_timeout: 5s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "order-service"
      - target_label: service
        replacement: "order-service"

  - job_name: "notification-service"
    static_configs:
      - targets: ["notification-service:2112"]
    metrics_path: "/metrics"
    scrape_interval: 10s
    scrape_timeout: 5s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "notification-service"
      - target_label: service
        replacement: "notification-service"
