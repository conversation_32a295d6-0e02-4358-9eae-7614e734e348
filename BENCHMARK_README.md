# Coupon Microservice Benchmark System

A comprehensive benchmarking system for all services in the coupon microservice architecture.

## 🏗️ Architecture Overview

The benchmark system provides multiple ways to run performance tests:

1. **Root-level Makefile** - Run benchmarks for all services from the project root
2. **Individual Service Makefiles** - Run benchmarks from within each service directory
3. **Monitoring Stack Benchmarks** - Specialized benchmarks for the monitoring infrastructure

## 🚀 Quick Start

### Run All Service Benchmarks
```bash
# From project root
make benchmark-all

# Quick benchmarks (10s duration)
make benchmark-quick

# Load testing (60s duration, 20 concurrency)
make benchmark-load

# Stress testing (120s duration, 50 concurrency)
make benchmark-stress
```

### Run Individual Service Benchmarks
```bash
# From project root
make benchmark-auth
make benchmark-user
make benchmark-voucher
make benchmark-product
make benchmark-order
make benchmark-notification
make benchmark-api-gateway
make benchmark-shared-libs

# From individual service directories
cd coupon-auth-service && make benchmark
cd coupon-user-service && make benchmark-quick
cd coupon-voucher-service && make benchmark-load
```

## 📊 Available Commands

### Root-Level Commands (from project root)

| Command | Description |
|---------|-------------|
| `make benchmark-all` | Run benchmarks for all services |
| `make benchmark-auth` | Run auth service benchmarks only |
| `make benchmark-user` | Run user service benchmarks only |
| `make benchmark-voucher` | Run voucher service benchmarks only |
| `make benchmark-product` | Run product service benchmarks only |
| `make benchmark-order` | Run order service benchmarks only |
| `make benchmark-notification` | Run notification service benchmarks only |
| `make benchmark-api-gateway` | Run API gateway benchmarks only |
| `make benchmark-shared-libs` | Run shared libraries benchmarks only |
| `make benchmark-quick` | Run quick benchmarks (10s) for all services |
| `make benchmark-load` | Run load benchmarks (60s, 20 concurrency) |
| `make benchmark-stress` | Run stress benchmarks (120s, 50 concurrency) |
| `make benchmark-monitoring` | Run monitoring stack benchmarks |
| `make benchmark-report` | Generate comprehensive benchmark report |
| `make benchmark-clean` | Clean all benchmark results |

### Service-Level Commands (from service directories)

| Command | Description |
|---------|-------------|
| `make benchmark` | Run standard benchmarks for the service |
| `make benchmark-quick` | Run quick benchmarks (10s duration) |
| `make benchmark-load` | Run load benchmarks (60s duration) |
| `make benchmark-stress` | Run stress benchmarks (120s duration) |
| `make benchmark-clean` | Clean benchmark results for the service |

## ⚙️ Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `BENCHMARK_DURATION` | `30s` | Duration per benchmark test |
| `BENCHMARK_CONCURRENCY` | `10` | Concurrency level for load tests |
| `BENCHMARK_TIMEOUT` | `30m` | Maximum timeout for benchmark execution |

### Examples
```bash
# Custom duration
make benchmark-all BENCHMARK_DURATION=60s

# Custom concurrency
make benchmark-load BENCHMARK_CONCURRENCY=50

# Custom timeout
make benchmark-stress BENCHMARK_TIMEOUT=60m
```

## 📁 Directory Structure

```
coupon-microservice/
├── Makefile                           # Root benchmark commands
├── benchmark-results/                 # Generated benchmark results
│   ├── auth-service_benchmark_*.txt
│   ├── user-service_benchmark_*.txt
│   └── comprehensive_report_*.md
├── scripts/
│   └── add-benchmark-targets.sh      # Script to add benchmark targets
├── coupon-auth-service/
│   ├── Makefile                      # Service-specific benchmark commands
│   └── benchmark/
│       └── benchmark_test.go         # Benchmark tests
├── coupon-user-service/
│   ├── Makefile
│   └── benchmark/
│       └── benchmark_test.go
└── ... (other services)
```

## 🔧 Setup Instructions

### 1. Add Benchmark Targets to Service Makefiles
```bash
# Run the setup script to add benchmark targets to all services
./scripts/add-benchmark-targets.sh
```

### 2. Verify Benchmark Tests Exist
Each service should have a `benchmark/` directory with `*_test.go` files containing benchmark functions:

```go
// Example benchmark test
func BenchmarkServiceOperation(b *testing.B) {
    for i := 0; i < b.N; i++ {
        // Your benchmark code here
    }
}
```

### 3. Ensure Services Are Running
Before running benchmarks, make sure the services are running:
```bash
# Start all services
docker-compose up -d

# Or start individual services
cd coupon-auth-service && make compose-up
```

## 📈 Benchmark Results

### Result Files
- **Individual Results**: `benchmark-results/{service}_benchmark_{timestamp}.txt`
- **Comprehensive Report**: `benchmark-results/comprehensive_report_{timestamp}.md`

### Result Format
```
BenchmarkServiceOperation-8    1000000    1234 ns/op    456 B/op    7 allocs/op
```

Where:
- `BenchmarkServiceOperation-8`: Benchmark name with GOMAXPROCS
- `1000000`: Number of iterations
- `1234 ns/op`: Nanoseconds per operation
- `456 B/op`: Bytes allocated per operation
- `7 allocs/op`: Allocations per operation

## 🔍 Monitoring Integration

### Monitoring Stack Benchmarks
```bash
# Run monitoring-specific benchmarks
make benchmark-monitoring

# Or from monitoring directory
cd coupon-monitoring && make benchmark-all
```

### Metrics Collection
The monitoring stack can collect benchmark metrics in real-time:
- **Prometheus**: Scrapes benchmark metrics from services
- **Grafana**: Visualizes benchmark performance over time
- **Alertmanager**: Alerts on performance regressions

## 🛠️ Troubleshooting

### Common Issues

#### 1. No Benchmark Directory Found
**Problem**: `No benchmark directory found for {service}`
**Solution**: Create a `benchmark/` directory in the service with `*_test.go` files

#### 2. Benchmark Tests Fail
**Problem**: Benchmark execution fails
**Solution**: 
- Check if services are running
- Verify benchmark test syntax
- Check service dependencies

#### 3. Permission Denied
**Problem**: Cannot write to benchmark-results directory
**Solution**: 
```bash
mkdir -p benchmark-results
chmod 755 benchmark-results
```

### Debugging Commands
```bash
# Check if services are running
docker-compose ps

# View service logs
docker-compose logs {service-name}

# Test individual benchmark
cd coupon-auth-service
go test -bench=. -v ./benchmark/...
```

## 📊 Performance Analysis

### Interpreting Results
1. **ns/op (nanoseconds per operation)**: Lower is better
2. **B/op (bytes per operation)**: Lower indicates less memory usage
3. **allocs/op (allocations per operation)**: Lower reduces GC pressure

### Performance Targets
- **API Endpoints**: < 100ms response time
- **Database Operations**: < 50ms query time
- **Cache Operations**: < 1ms access time
- **Memory Usage**: < 100MB per service

### Optimization Tips
1. **Profile hot paths**: Use `go tool pprof` for detailed analysis
2. **Optimize database queries**: Add indexes, reduce N+1 queries
3. **Implement caching**: Use Redis for frequently accessed data
4. **Connection pooling**: Optimize database and HTTP connections

## 🤝 Contributing

### Adding New Benchmarks
1. Create benchmark functions in `{service}/benchmark/benchmark_test.go`
2. Follow Go benchmark naming convention: `BenchmarkXxx`
3. Use `b.ResetTimer()` to exclude setup time
4. Test with different input sizes using sub-benchmarks

### Example Benchmark
```go
func BenchmarkUserService_CreateUser(b *testing.B) {
    // Setup
    service := setupUserService()
    user := &pb.CreateUserRequest{
        Email: "<EMAIL>",
        Name:  "Test User",
    }
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.CreateUser(context.Background(), user)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

## 📚 Additional Resources

- [Go Benchmarking Guide](https://golang.org/pkg/testing/#hdr-Benchmarks)
- [Performance Optimization Best Practices](https://github.com/golang/go/wiki/Performance)
- [Prometheus Monitoring](https://prometheus.io/docs/guides/go-application/)
- [Grafana Dashboards](https://grafana.com/docs/grafana/latest/dashboards/)
