#!/bin/bash

# Script to fix benchmark configuration loading across all services

set -e

SERVICES=(
    "coupon-user-service"
    "coupon-voucher-service" 
    "coupon-product-service"
    "coupon-order-service"
    "coupon-notification-service"
    "coupon-api-gateway"
)

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration loading functions to add to benchmark files
CONFIG_FUNCTIONS='
// loadBenchmarkConfig loads configuration for benchmark tests
// It looks for config files in the parent directory structure
func loadBenchmarkConfig() (*config.Config, error) {
	// Try to load from parent directory first (where the actual service config is)
	configPaths := []string{
		"../config/config.yaml",    // Most likely location
		"./config.yaml",            // Current directory
		"../config.yaml",           // Parent directory
		"../../config/config.yaml", // Two levels up
	}

	for _, configPath := range configPaths {
		if _, err := os.Stat(configPath); err == nil {
			// Found config file, load it using a custom viper instance
			cfg, err := loadConfigFromPath(configPath)
			if err == nil {
				return cfg, nil
			}
		}
	}

	// Fallback to default config loading (will likely fail but provides proper error)
	return config.Load()
}

// loadConfigFromPath loads config from a specific file path
func loadConfigFromPath(configPath string) (*config.Config, error) {
	v := viper.New()
	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Set the same defaults as the main config loader
	setConfigDefaults(v)

	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	var cfg config.Config
	if err := v.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &cfg, nil
}

// setConfigDefaults sets the same defaults as the shared config package
func setConfigDefaults(v *viper.Viper) {
	v.SetDefault("service.environment", "development")
	v.SetDefault("service.port", 8080)
	v.SetDefault("service.grpc_port", 50051)
	v.SetDefault("database.host", "localhost")
	v.SetDefault("database.port", 5432)
	v.SetDefault("database.ssl_mode", "disable")
	v.SetDefault("database.max_open_conns", 25)
	v.SetDefault("database.max_idle_conns", 25)
	v.SetDefault("redis.host", "localhost")
	v.SetDefault("redis.port", 6379)
	v.SetDefault("redis.db", 0)
	v.SetDefault("grpc.host", "localhost")
	v.SetDefault("grpc.max_receive_size", 4*1024*1024)
	v.SetDefault("grpc.max_send_size", 4*1024*1024)
	v.SetDefault("logging.level", "info")
	v.SetDefault("logging.format", "json")
	v.SetDefault("metrics.path", "/metrics")
}'

fix_benchmark_config() {
    local service_dir=$1
    local service_name=$(basename "$service_dir")
    local benchmark_file="$service_dir/benchmark/benchmark_test.go"
    
    print_status "Processing $service_name..."
    
    if [ ! -f "$benchmark_file" ]; then
        print_warning "No benchmark file found in $service_dir"
        return 0
    fi
    
    # Check if already fixed
    if grep -q "loadBenchmarkConfig" "$benchmark_file"; then
        print_warning "Benchmark config already fixed in $service_name"
        return 0
    fi
    
    # Create backup
    cp "$benchmark_file" "$benchmark_file.backup"
    
    # Add viper import if not present
    if ! grep -q "github.com/spf13/viper" "$benchmark_file"; then
        # Find the import section and add viper
        sed -i.tmp '/import (/,/)/ {
            /google.golang.org\/grpc/i\
	"github.com/spf13/viper"
        }' "$benchmark_file"
        rm -f "$benchmark_file.tmp"
    fi
    
    # Add config import if not present
    if ! grep -q "coupon-shared-libs/config" "$benchmark_file"; then
        # Find the import section and add config
        sed -i.tmp '/import (/,/)/ {
            /coupon-shared-libs\/benchmark/a\
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
        }' "$benchmark_file"
        rm -f "$benchmark_file.tmp"
    fi
    
    # Replace config.Load() with loadBenchmarkConfig()
    sed -i.tmp 's/config\.Load()/loadBenchmarkConfig()/g' "$benchmark_file"
    rm -f "$benchmark_file.tmp"
    
    # Add the configuration functions after imports
    # Find the line after the imports and add our functions
    awk '
        /^import \(/ { in_import = 1 }
        /^\)/ && in_import { 
            print $0
            print ""
            print "'"$CONFIG_FUNCTIONS"'"
            print ""
            in_import = 0
            next
        }
        { print }
    ' "$benchmark_file" > "$benchmark_file.tmp" && mv "$benchmark_file.tmp" "$benchmark_file"
    
    print_success "Fixed benchmark config in $service_name"
}

main() {
    echo "=================================================="
    echo "  Fixing Benchmark Configuration Loading"
    echo "=================================================="
    echo ""
    
    # Fix auth service (already done manually, but check)
    if [ -f "coupon-auth-service/benchmark/benchmark_test.go" ]; then
        if grep -q "loadBenchmarkConfig" "coupon-auth-service/benchmark/benchmark_test.go"; then
            print_success "Auth service benchmark config already fixed"
        else
            print_warning "Auth service benchmark config needs manual review"
        fi
    fi
    
    # Fix other services
    for service in "${SERVICES[@]}"; do
        if [ -d "$service" ]; then
            fix_benchmark_config "$service"
        else
            print_warning "Service directory not found: $service"
        fi
        echo ""
    done
    
    print_success "Benchmark configuration fixes completed!"
    echo ""
    echo "Test the fixes by running:"
    echo "  cd coupon-auth-service && make benchmark"
    echo "  cd coupon-user-service && make benchmark"
    echo "  etc..."
}

main "$@"
