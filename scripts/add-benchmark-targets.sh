#!/bin/bash

# Script to add benchmark targets to all service Makefiles

set -e

SERVICES=(
    "coupon-user-service"
    "coupon-voucher-service" 
    "coupon-product-service"
    "coupon-order-service"
    "coupon-notification-service"
    "coupon-api-gateway"
    "coupon-shared-libs"
)

BENCHMARK_TARGETS='
# Benchmark targets
BENCHMARK_DURATION ?= 30s
BENCHMARK_TIMEOUT ?= 30m
TIMESTAMP := $(shell date +%Y%m%d_%H%M%S)

benchmark:
	@echo "Running SERVICE_NAME benchmarks..."
	@mkdir -p benchmark-results
	@go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
		-benchtime=$(BENCHMARK_DURATION) \
		./benchmark/... > benchmark-results/SERVICE_NAME_benchmark_$(TIMESTAMP).txt 2>&1
	@echo "Benchmark completed. Results saved in benchmark-results/"

benchmark-quick:
	@$(MAKE) benchmark BENCHMARK_DURATION=10s BENCHMARK_TIMEOUT=10m

benchmark-load:
	@$(MAKE) benchmark BENCHMARK_DURATION=60s BENCHMARK_TIMEOUT=60m

benchmark-stress:
	@$(MAKE) benchmark BENCHMARK_DURATION=120s BENCHMARK_TIMEOUT=120m

benchmark-clean:
	@rm -rf benchmark-results
	@echo "Benchmark results cleaned"'

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

add_benchmark_targets() {
    local service_dir=$1
    local service_name=$(basename "$service_dir")
    local makefile="$service_dir/Makefile"
    
    print_status "Processing $service_name..."
    
    if [ ! -f "$makefile" ]; then
        print_warning "Makefile not found in $service_dir"
        return 1
    fi
    
    # Check if benchmark targets already exist
    if grep -q "^benchmark:" "$makefile"; then
        print_warning "Benchmark targets already exist in $service_name Makefile"
        return 0
    fi
    
    # Create backup
    cp "$makefile" "$makefile.backup"
    
    # Add benchmark targets to .PHONY line
    if grep -q "^\.PHONY:" "$makefile"; then
        sed -i.tmp 's/^\.PHONY: \(.*\)$/.PHONY: \1 benchmark benchmark-quick benchmark-load benchmark-stress benchmark-clean/' "$makefile"
        rm -f "$makefile.tmp"
    else
        # Add .PHONY line if it doesn't exist
        echo ".PHONY: benchmark benchmark-quick benchmark-load benchmark-stress benchmark-clean" >> "$makefile"
    fi
    
    # Add benchmark targets at the end of the file
    local targets=$(echo "$BENCHMARK_TARGETS" | sed "s/SERVICE_NAME/$service_name/g")
    echo "$targets" >> "$makefile"
    
    print_success "Added benchmark targets to $service_name"
}

main() {
    echo "=================================================="
    echo "  Adding Benchmark Targets to Service Makefiles"
    echo "=================================================="
    echo ""
    
    for service in "${SERVICES[@]}"; do
        if [ -d "$service" ]; then
            add_benchmark_targets "$service"
        else
            print_warning "Service directory not found: $service"
        fi
        echo ""
    done
    
    print_success "Benchmark targets added to all available services!"
    echo ""
    echo "Usage examples:"
    echo "  cd coupon-user-service && make benchmark"
    echo "  cd coupon-voucher-service && make benchmark-quick"
    echo "  cd coupon-product-service && make benchmark-load"
    echo ""
    echo "Or use the root Makefile:"
    echo "  make benchmark-all"
    echo "  make benchmark-user"
    echo "  make benchmark-voucher"
}

main "$@"
