BINARY=auth-server
CLI_BINARY=auth-cli

.PHONY: build run test docker-build compose-up compose-down build-cli register-service benchmark benchmark-quick benchmark-load benchmark-stress benchmark-clean

build:
	go build -o $(BINARY) ./cmd/server

build-cli:
	go build -o $(CLI_BINARY) ./cmd/cli

run: build
	./$(BINARY)

register-service: build-cli
	@if [ -z "$(SERVICE_NAME)" ]; then \
		echo "Error: SERVICE_NAME is required"; \
		echo "Usage: make register-service SERVICE_NAME=user-service [SERVICE_VERSION=1.0.0] [DESCRIPTION='Service description']"; \
		exit 1; \
	fi
	@echo "🚀 Simulating CI/CD Service Registration for $(SERVICE_NAME)"
	@echo "=================================================="
	./$(CLI_BINARY) \
		-service-name="$(SERVICE_NAME)" \
		-service-version="$(if $(SERVICE_VERSION),$(SERVICE_VERSION),1.0.0)" \
		-description="$(if $(DESCRIPTION),$(DESCRIPTION),Microservice: $(SERVICE_NAME))" \
		-auth-addr="localhost:50052"


test:
	go test ./...

docker-build:
	docker build -t coupon-auth-service .

compose-up:
	docker-compose up -d

compose-down:
	docker-compose down

# Benchmark targets
BENCHMARK_DURATION ?= 30s
BENCHMARK_TIMEOUT ?= 30m
TIMESTAMP := $(shell date +%Y%m%d_%H%M%S)

benchmark:
	@echo "Running auth service benchmarks..."
	@mkdir -p benchmark-results
	@go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
		-benchtime=$(BENCHMARK_DURATION) \
		./benchmark/... > benchmark-results/auth-service_benchmark_$(TIMESTAMP).txt 2>&1
	@echo "Benchmark completed. Results saved in benchmark-results/"

benchmark-quick:
	@$(MAKE) benchmark BENCHMARK_DURATION=10s BENCHMARK_TIMEOUT=10m

benchmark-load:
	@$(MAKE) benchmark BENCHMARK_DURATION=60s BENCHMARK_TIMEOUT=60m

benchmark-stress:
	@$(MAKE) benchmark BENCHMARK_DURATION=120s BENCHMARK_TIMEOUT=120m

benchmark-clean:
	@rm -rf benchmark-results
	@echo "Benchmark results cleaned"
