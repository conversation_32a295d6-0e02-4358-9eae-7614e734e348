package benchmark

import (
	"testing"
)

// TestConfigLoading tests that the benchmark configuration loading works
func TestConfigLoading(t *testing.T) {
	cfg, err := loadBenchmarkConfig()
	if err != nil {
		t.Fatalf("Failed to load benchmark config: %v", err)
	}

	if cfg == nil {
		t.<PERSON>("Config is nil")
	}

	// Test that basic config values are loaded
	if cfg.Service.Port == 0 {
		t.<PERSON><PERSON>("Service port not loaded")
	}

	if cfg.Database.Host == "" {
		t.Error("Database host not loaded")
	}

	if cfg.Redis.Host == "" {
		t.Error("Redis host not loaded")
	}

	t.Logf("Config loaded successfully:")
	t.Logf("  Service Port: %d", cfg.Service.Port)
	t.Logf("  Database Host: %s", cfg.Database.Host)
	t.Logf("  Redis Host: %s", cfg.Redis.Host)
	t.Logf("  Metrics Path: %s", cfg.Metrics.Path)
}
