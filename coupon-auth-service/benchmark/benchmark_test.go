package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/spf13/viper"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"

	authv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

// loadBenchmarkConfig loads configuration for benchmark tests
// It looks for config files in the parent directory structure
func loadBenchmarkConfig() (*config.Config, error) {
	// Try to load from parent directory first (where the actual service config is)
	configPaths := []string{
		"../config/config.yaml",    // Most likely location
		"./config.yaml",            // Current directory
		"../config.yaml",           // Parent directory
		"../../config/config.yaml", // Two levels up
	}

	for _, configPath := range configPaths {
		if _, err := os.Stat(configPath); err == nil {
			// Found config file, load it using a custom viper instance
			cfg, err := loadConfigFromPath(configPath)
			if err == nil {
				return cfg, nil
			}
		}
	}

	// Fallback to default config loading (will likely fail but provides proper error)
	return config.Load()
}

// loadConfigFromPath loads config from a specific file path
func loadConfigFromPath(configPath string) (*config.Config, error) {
	v := viper.New()
	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Set the same defaults as the main config loader
	setConfigDefaults(v)

	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	var cfg config.Config
	if err := v.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &cfg, nil
}

// setConfigDefaults sets the same defaults as the shared config package
func setConfigDefaults(v *viper.Viper) {
	v.SetDefault("service.environment", "development")
	v.SetDefault("service.port", 8080)
	v.SetDefault("service.grpc_port", 50051)
	v.SetDefault("database.host", "localhost")
	v.SetDefault("database.port", 5432)
	v.SetDefault("database.ssl_mode", "disable")
	v.SetDefault("database.max_open_conns", 25)
	v.SetDefault("database.max_idle_conns", 25)
	v.SetDefault("redis.host", "localhost")
	v.SetDefault("redis.port", 6379)
	v.SetDefault("redis.db", 0)
	v.SetDefault("grpc.host", "localhost")
	v.SetDefault("grpc.max_receive_size", 4*1024*1024)
	v.SetDefault("grpc.max_send_size", 4*1024*1024)
	v.SetDefault("logging.level", "info")
	v.SetDefault("logging.format", "json")
	v.SetDefault("metrics.path", "/metrics")
}

var (
	authGrpcSuite  *benchmark.GRPCBenchmarkSuite
	authDBSuite    *benchmark.DatabaseBenchmarkSuite
	authCacheSuite *benchmark.CacheBenchmarkSuite
	authTestDB     *database.DB
	authTestRedis  *redis.Client
	authGenerator  *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupAuthBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup auth benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	cleanupAuth()
	os.Exit(code)
}

func setupAuthBenchmarkEnvironment() error {
	cfg, err := loadBenchmarkConfig()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("auth-service-benchmark")

	authTestDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	authTestRedis = redis.NewClient(&cfg.Redis, logger, appMetrics)

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50051",
		ServiceName:    "auth-service",
		Timeout:        5 * time.Second,
	}

	authGrpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "auth-service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	authDBSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, authTestDB.DB)

	authCacheSuite = benchmark.NewCacheBenchmarkSuite("auth-service", authTestRedis)
	authGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanupAuth() {
	if authGrpcSuite != nil {
		authGrpcSuite.Close()
	}
	if authTestDB != nil {
		sqlDB, _ := authTestDB.DB.DB()
		sqlDB.Close()
	}
	if authTestRedis != nil {
		authTestRedis.Close()
	}
}

func BenchmarkAuthService_gRPC_ValidateServiceCredentials(b *testing.B) {
	authGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := authv1.NewAuthServiceClient(conn)

		req := &authv1.ValidateServiceCredentialsRequest{
			ClientId:    "test-client-id",
			ClientKey:   "test-client-key",
			ServiceName: "test-service",
		}

		_, err := client.ValidateServiceCredentials(ctx, req)
		return err
	})
}

func BenchmarkAuthService_gRPC_RegisterService(b *testing.B) {
	authGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := authv1.NewAuthServiceClient(conn)
		ctx = addAuthAuthMetadata(ctx)

		req := &authv1.RegisterServiceRequest{
			ServiceName:    fmt.Sprintf("test-service-%d", authGenerator.GenerateUserID()),
			ServiceVersion: "1.0.0",
			Description:    "Test service for benchmarking",
		}

		_, err := client.RegisterService(ctx, req)
		return err
	})
}

func BenchmarkAuthService_gRPC_HealthCheck(b *testing.B) {
	authGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := authv1.NewAuthServiceClient(conn)
		ctx = addAuthAuthMetadata(ctx)

		_, err := client.ValidateServiceCredentials(ctx, &authv1.ValidateServiceCredentialsRequest{
			ClientId:    "health-check-client",
			ClientKey:   "health-check-key",
			ServiceName: "health-check",
		})
		return err
	})
}

func BenchmarkAuthService_Database_ValidateServiceCredentials(b *testing.B) {
	authDBSuite.BenchmarkQuery(b, "validate_service_credentials", func(ctx context.Context, db *gorm.DB) error {
		var credential struct {
			ID        int    `gorm:"column:id"`
			ClientID  string `gorm:"column:client_id"`
			ClientKey string `gorm:"column:client_key"`
			IsActive  bool   `gorm:"column:is_active"`
		}

		return db.Table("service_credentials").
			Where("client_id = ? AND client_key = ? AND is_active = true",
				"test-client-id", "test-client-key").
			First(&credential).Error
	})
}

func BenchmarkAuthService_Database_CreateServiceCredential(b *testing.B) {
	authDBSuite.BenchmarkQuery(b, "create_service_credential", func(ctx context.Context, db *gorm.DB) error {
		credential := map[string]any{
			"client_id":    fmt.Sprintf("client_%d", authGenerator.GenerateUserID()),
			"client_key":   fmt.Sprintf("key_%d", authGenerator.GenerateUserID()),
			"service_name": "test-service",
			"is_active":    true,
			"created_at":   time.Now(),
			"updated_at":   time.Now(),
		}
		return db.Table("service_credentials").Create(credential).Error
	})
}

func BenchmarkAuthService_Database_TokenOperations(b *testing.B) {
	authDBSuite.BenchmarkTransaction(b, "token_operations", func(ctx context.Context, tx *gorm.DB) error {
		token := map[string]any{
			"user_id":      authGenerator.GenerateUserID(),
			"token_hash":   fmt.Sprintf("hash_%d", authGenerator.GenerateUserID()),
			"refresh_hash": fmt.Sprintf("refresh_%d", authGenerator.GenerateUserID()),
			"expires_at":   time.Now().Add(24 * time.Hour),
			"is_active":    true,
			"created_at":   time.Now(),
		}

		if err := tx.Table("user_tokens").Create(token).Error; err != nil {
			return err
		}

		return tx.Table("user_tokens").
			Where("id = ?", token["id"]).
			Update("last_used_at", time.Now()).Error
	})
}

func BenchmarkAuthService_Database_RevokeToken(b *testing.B) {
	authDBSuite.BenchmarkQuery(b, "revoke_token", func(ctx context.Context, db *gorm.DB) error {
		return db.Table("user_tokens").
			Where("token_hash = ?", fmt.Sprintf("hash_%d", authGenerator.GenerateUserID())).
			Update("is_active", false).Error
	})
}

func BenchmarkAuthService_Cache_TokenValidation(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "auth-service",
		TestName:       "cache_token_validation",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		tokenHash := fmt.Sprintf("token_hash_%d", authGenerator.GenerateUserID())
		cacheKey := fmt.Sprintf("token:validation:%s", tokenHash)

		_, err := authTestRedis.Get(ctx, cacheKey)
		if err == nil {
			return nil
		}

		validationResult := map[string]any{
			"valid":   true,
			"user_id": authGenerator.GenerateUserID(),
			"expires": time.Now().Add(time.Hour).Unix(),
		}

		return authTestRedis.Set(ctx, cacheKey, validationResult, 15*time.Minute)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkAuthService_Cache_ServiceCredentials(b *testing.B) {
	authCacheSuite.BenchmarkCacheGet(b, func(ctx context.Context, key string) error {
		cacheKey := fmt.Sprintf("service_creds:%s", key)
		_, err := authTestRedis.Get(ctx, cacheKey)
		return err
	})
}

func BenchmarkAuthService_Cache_SetServiceCredentials(b *testing.B) {
	authCacheSuite.BenchmarkCacheSet(b, func(ctx context.Context, key string, value any) error {
		cacheKey := fmt.Sprintf("service_creds:%s", key)
		credData := map[string]any{
			"client_id":    key,
			"service_name": "test-service",
			"is_active":    true,
		}
		return authTestRedis.Set(ctx, cacheKey, credData, time.Hour)
	})
}

func BenchmarkAuthService_JWTGeneration(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "auth-service",
		TestName:       "jwt_generation_performance",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx = addAuthAuthMetadata(ctx)

		conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			return err
		}
		defer conn.Close()

		client := authv1.NewAuthServiceClient(conn)
		req := &authv1.ValidateServiceCredentialsRequest{
			ClientId:    "benchmark-client",
			ClientKey:   "benchmark-key",
			ServiceName: "benchmark-service",
		}

		_, err = client.ValidateServiceCredentials(ctx, req)
		return err
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkAuthService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "auth-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 20,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "validate_service_credentials",
				Weight: 50,
				Execute: func(ctx context.Context) error {
					conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := authv1.NewAuthServiceClient(conn)
					req := &authv1.ValidateServiceCredentialsRequest{
						ClientId:    "test-client-id",
						ClientKey:   "test-client-key",
						ServiceName: "test-service",
					}
					_, err = client.ValidateServiceCredentials(ctx, req)
					return err
				},
			},
			{
				Name:   "register_service",
				Weight: 50,
				Execute: func(ctx context.Context) error {
					ctx = addAuthAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := authv1.NewAuthServiceClient(conn)
					req := &authv1.RegisterServiceRequest{
						ServiceName:    fmt.Sprintf("service-%d", authGenerator.GenerateUserID()),
						ServiceVersion: "1.0.0",
						Description:    "Benchmark test service",
					}
					_, err = client.RegisterService(ctx, req)
					return err
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

func addAuthAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "auth-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}
