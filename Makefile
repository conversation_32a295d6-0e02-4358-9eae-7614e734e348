.PHONY: help benchmark-setup benchmark-fix-config benchmark-all benchmark-auth benchmark-user benchmark-voucher benchmark-product benchmark-order benchmark-notification benchmark-api-gateway benchmark-shared-libs benchmark-clean benchmark-report benchmark-quick benchmark-load benchmark-stress benchmark-monitoring

# Colors for output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
NC=\033[0m

# Configuration
TIMESTAMP := $(shell date +%Y%m%d_%H%M%S)
RESULTS_DIR := benchmark-results
BENCHMARK_DURATION ?= 30s
BENCHMARK_CONCURRENCY ?= 10
BENCHMARK_TIMEOUT ?= 30m

# Service directories
AUTH_SERVICE_DIR := coupon-auth-service
USER_SERVICE_DIR := coupon-user-service
VOUCHER_SERVICE_DIR := coupon-voucher-service
PRODUCT_SERVICE_DIR := coupon-product-service
ORDER_SERVICE_DIR := coupon-order-service
NOTIFICATION_SERVICE_DIR := coupon-notification-service
API_GATEWAY_DIR := coupon-api-gateway
SHARED_LIBS_DIR := coupon-shared-libs
MONITORING_DIR := coupon-monitoring

help:
	@echo "=================================================="
	@echo "  Coupon Microservice - Benchmark Commands"
	@echo "=================================================="
	@echo ""
	@echo "Individual Service Benchmarks:"
	@echo "  benchmark-auth         - Run auth service benchmarks"
	@echo "  benchmark-user         - Run user service benchmarks"
	@echo "  benchmark-voucher      - Run voucher service benchmarks"
	@echo "  benchmark-product      - Run product service benchmarks"
	@echo "  benchmark-order        - Run order service benchmarks"
	@echo "  benchmark-notification - Run notification service benchmarks"
	@echo "  benchmark-api-gateway  - Run API gateway benchmarks"
	@echo "  benchmark-shared-libs  - Run shared libraries benchmarks"
	@echo ""
	@echo "Comprehensive Benchmarks:"
	@echo "  benchmark-all          - Run benchmarks for all services"
	@echo "  benchmark-quick        - Run quick benchmarks (10s duration)"
	@echo "  benchmark-load         - Run load benchmarks (60s, 20 concurrency)"
	@echo "  benchmark-stress       - Run stress benchmarks (120s, 50 concurrency)"
	@echo ""
	@echo "Setup & Management:"
	@echo "  benchmark-setup        - Add benchmark targets to all service Makefiles"
	@echo "  benchmark-fix-config   - Fix benchmark configuration loading issues"
	@echo ""
	@echo "Monitoring & Reporting:"
	@echo "  benchmark-monitoring   - Run monitoring stack benchmarks"
	@echo "  benchmark-report       - Generate comprehensive benchmark report"
	@echo "  benchmark-clean        - Clean benchmark results"
	@echo ""
	@echo "Configuration (Environment Variables):"
	@echo "  BENCHMARK_DURATION     - Duration per benchmark (default: 30s)"
	@echo "  BENCHMARK_CONCURRENCY  - Concurrency level (default: 10)"
	@echo "  BENCHMARK_TIMEOUT      - Test timeout (default: 30m)"
	@echo ""
	@echo "Examples:"
	@echo "  make benchmark-auth"
	@echo "  make benchmark-all BENCHMARK_DURATION=60s"
	@echo "  make benchmark-load"
	@echo "  make benchmark-report"

# Setup target
benchmark-setup:
	@echo -e "$(BLUE)[INFO]$(NC) Setting up benchmark targets in all service Makefiles..."
	@chmod +x scripts/add-benchmark-targets.sh
	@./scripts/add-benchmark-targets.sh
	@echo -e "$(GREEN)[SUCCESS]$(NC) Benchmark setup completed!"

# Fix benchmark configuration loading
benchmark-fix-config:
	@echo -e "$(BLUE)[INFO]$(NC) Fixing benchmark configuration loading across all services..."
	@chmod +x scripts/fix-benchmark-config.sh
	@./scripts/fix-benchmark-config.sh
	@echo -e "$(GREEN)[SUCCESS]$(NC) Benchmark configuration fixes completed!"

# Individual service benchmark targets
benchmark-auth:
	@echo -e "$(BLUE)[INFO]$(NC) Running auth service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(AUTH_SERVICE_DIR)/benchmark" ]; then \
		cd $(AUTH_SERVICE_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/auth-service_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) Auth service benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) Auth service benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for auth service"; \
	fi

benchmark-user:
	@echo -e "$(BLUE)[INFO]$(NC) Running user service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(USER_SERVICE_DIR)/benchmark" ]; then \
		cd $(USER_SERVICE_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/user-service_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) User service benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) User service benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for user service"; \
	fi

benchmark-voucher:
	@echo -e "$(BLUE)[INFO]$(NC) Running voucher service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(VOUCHER_SERVICE_DIR)/benchmark" ]; then \
		cd $(VOUCHER_SERVICE_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/voucher-service_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) Voucher service benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) Voucher service benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for voucher service"; \
	fi

benchmark-product:
	@echo -e "$(BLUE)[INFO]$(NC) Running product service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(PRODUCT_SERVICE_DIR)/benchmark" ]; then \
		cd $(PRODUCT_SERVICE_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/product-service_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) Product service benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) Product service benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for product service"; \
	fi

benchmark-order:
	@echo -e "$(BLUE)[INFO]$(NC) Running order service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(ORDER_SERVICE_DIR)/benchmark" ]; then \
		cd $(ORDER_SERVICE_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/order-service_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) Order service benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) Order service benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for order service"; \
	fi

benchmark-notification:
	@echo -e "$(BLUE)[INFO]$(NC) Running notification service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(NOTIFICATION_SERVICE_DIR)/benchmark" ]; then \
		cd $(NOTIFICATION_SERVICE_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/notification-service_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) Notification service benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) Notification service benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for notification service"; \
	fi

benchmark-api-gateway:
	@echo -e "$(BLUE)[INFO]$(NC) Running API gateway benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(API_GATEWAY_DIR)/benchmark" ]; then \
		cd $(API_GATEWAY_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/api-gateway_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) API gateway benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) API gateway benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for API gateway"; \
	fi

benchmark-shared-libs:
	@echo -e "$(BLUE)[INFO]$(NC) Running shared libraries benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@if [ -d "$(SHARED_LIBS_DIR)/benchmark" ]; then \
		cd $(SHARED_LIBS_DIR) && \
		go test -bench=. -benchmem -timeout=$(BENCHMARK_TIMEOUT) \
			-benchtime=$(BENCHMARK_DURATION) \
			./benchmark/... > ../$(RESULTS_DIR)/shared-libs_benchmark_$(TIMESTAMP).txt 2>&1 && \
		echo -e "$(GREEN)[SUCCESS]$(NC) Shared libraries benchmarks completed" || \
		echo -e "$(RED)[ERROR]$(NC) Shared libraries benchmarks failed"; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No benchmark directory found for shared libraries"; \
	fi

# Comprehensive benchmark targets
benchmark-all: benchmark-auth benchmark-user benchmark-voucher benchmark-product benchmark-order benchmark-notification benchmark-api-gateway benchmark-shared-libs
	@echo -e "$(GREEN)[SUCCESS]$(NC) All service benchmarks completed!"
	@echo -e "$(BLUE)[INFO]$(NC) Results saved in: $(RESULTS_DIR)/"
	@echo -e "$(BLUE)[INFO]$(NC) Run 'make benchmark-report' to generate a summary report"

benchmark-quick:
	@echo -e "$(BLUE)[INFO]$(NC) Running quick benchmarks (10s duration)..."
	@$(MAKE) benchmark-all BENCHMARK_DURATION=10s BENCHMARK_TIMEOUT=10m

benchmark-load:
	@echo -e "$(BLUE)[INFO]$(NC) Running load benchmarks (60s duration, 20 concurrency)..."
	@$(MAKE) benchmark-all BENCHMARK_DURATION=60s BENCHMARK_CONCURRENCY=20 BENCHMARK_TIMEOUT=60m

benchmark-stress:
	@echo -e "$(BLUE)[INFO]$(NC) Running stress benchmarks (120s duration, 50 concurrency)..."
	@$(MAKE) benchmark-all BENCHMARK_DURATION=120s BENCHMARK_CONCURRENCY=50 BENCHMARK_TIMEOUT=120m

# Monitoring benchmarks
benchmark-monitoring:
	@echo -e "$(BLUE)[INFO]$(NC) Running monitoring stack benchmarks..."
	@cd $(MONITORING_DIR) && $(MAKE) benchmark-all

# Report generation
benchmark-report:
	@echo -e "$(BLUE)[INFO]$(NC) Generating comprehensive benchmark report..."
	@mkdir -p $(RESULTS_DIR)
	@echo "# Coupon Microservice Benchmark Report" > $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "**Generated:** $(shell date)" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "**Timestamp:** $(TIMESTAMP)" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "**Configuration:**" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "- Duration: $(BENCHMARK_DURATION)" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "- Concurrency: $(BENCHMARK_CONCURRENCY)" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "- Timeout: $(BENCHMARK_TIMEOUT)" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "## Service Results" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md
	@for service in auth-service user-service voucher-service product-service order-service notification-service api-gateway shared-libs; do \
		echo "### $$service" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
		echo "" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
		result_file="$(RESULTS_DIR)/$${service}_benchmark_$(TIMESTAMP).txt"; \
		if [ -f "$$result_file" ]; then \
			echo '```' >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
			grep "^Benchmark" "$$result_file" | head -10 >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md 2>/dev/null || echo "No benchmark results found" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
			echo '```' >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
			echo "" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
			if grep -q "FAIL" "$$result_file"; then \
				echo "⚠️ **Status:** Some tests failed" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
			else \
				echo "✅ **Status:** All tests passed" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
			fi; \
		else \
			echo "❌ **Status:** No results file found" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
		fi; \
		echo "" >> $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md; \
	done
	@echo -e "$(GREEN)[SUCCESS]$(NC) Comprehensive report generated: $(RESULTS_DIR)/comprehensive_report_$(TIMESTAMP).md"

# Cleanup
benchmark-clean:
	@echo -e "$(BLUE)[INFO]$(NC) Cleaning benchmark results..."
	@rm -rf $(RESULTS_DIR)
	@echo -e "$(GREEN)[SUCCESS]$(NC) Benchmark results cleaned"
