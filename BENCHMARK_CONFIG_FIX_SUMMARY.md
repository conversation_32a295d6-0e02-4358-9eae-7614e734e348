# Benchmark Configuration Fix Summary

## 🔍 **Issue Identified**

**Problem**: Benchmark tests were failing due to configuration loading issues:
1. **Missing .env file**: Benchmarks looked for `.env` file in benchmark directory
2. **Config file not found**: Benchmarks searched for `config.yaml` in wrong locations:
   - `/Users/<USER>/Documents/coupon-auth-service/benchmark`
   - `/Users/<USER>/Documents/coupon-auth-service/benchmark/config`
   - `/etc/coupon-services/config`
3. **Incorrect path resolution**: Config files are actually located at `../config/config.yaml` relative to benchmark directory

## 🛠️ **Root Cause Analysis**

### **Configuration Loading Pattern**
The shared config package (`coupon-shared-libs/config`) uses this search pattern:
```go
func Load() (*Config, error) {
    v := viper.New()
    v.SetConfigName("config")
    v.SetConfigType("yaml")
    v.AddConfigPath(".")              // Current directory
    v.AddConfigPath("./config")       // ./config subdirectory  
    v.AddConfigPath("/etc/coupon-services/config") // System config
    // ...
}
```

**Problem**: When running benchmarks from `service/benchmark/` directory:
- Current directory is `service/benchmark/`
- Config file is actually at `service/config/config.yaml`
- Standard config loader can't find `../config/config.yaml`

## ✅ **Solution Implemented**

### **1. Custom Configuration Loading for Benchmarks**

**Added to**: `coupon-auth-service/benchmark/benchmark_test.go`

```go
// loadBenchmarkConfig loads configuration for benchmark tests
// It looks for config files in the parent directory structure
func loadBenchmarkConfig() (*config.Config, error) {
    configPaths := []string{
        "../config/config.yaml",    // Most likely location
        "./config.yaml",            // Current directory
        "../config.yaml",           // Parent directory
        "../../config/config.yaml", // Two levels up
    }
    
    for _, configPath := range configPaths {
        if _, err := os.Stat(configPath); err == nil {
            cfg, err := loadConfigFromPath(configPath)
            if err == nil {
                return cfg, nil
            }
        }
    }
    
    // Fallback to default config loading
    return config.Load()
}
```

### **2. Custom Config File Loader**

```go
// loadConfigFromPath loads config from a specific file path
func loadConfigFromPath(configPath string) (*config.Config, error) {
    v := viper.New()
    v.SetConfigFile(configPath)
    v.SetConfigType("yaml")
    
    // Set the same defaults as the main config loader
    setConfigDefaults(v)
    
    if err := v.ReadInConfig(); err != nil {
        return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
    }
    
    var cfg config.Config
    if err := v.Unmarshal(&cfg); err != nil {
        return nil, fmt.Errorf("failed to unmarshal config: %w", err)
    }
    
    return &cfg, nil
}
```

### **3. Configuration Defaults**

```go
// setConfigDefaults sets the same defaults as the shared config package
func setConfigDefaults(v *viper.Viper) {
    v.SetDefault("service.environment", "development")
    v.SetDefault("service.port", 8080)
    v.SetDefault("service.grpc_port", 50051)
    v.SetDefault("database.host", "localhost")
    v.SetDefault("database.port", 5432)
    // ... all other defaults
}
```

### **4. Updated Benchmark Setup**

**Before**:
```go
func setupAuthBenchmarkEnvironment() error {
    cfg, err := config.Load()  // ❌ Fails to find config
    if err != nil {
        return fmt.Errorf("failed to load config: %w", err)
    }
    // ...
}
```

**After**:
```go
func setupAuthBenchmarkEnvironment() error {
    cfg, err := loadBenchmarkConfig()  // ✅ Finds config correctly
    if err != nil {
        return fmt.Errorf("failed to load config: %w", err)
    }
    // ...
}
```

### **5. Added Required Imports**

```go
import (
    // ... existing imports
    "github.com/spf13/viper"  // Added for custom config loading
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"  // Added config import
)
```

## 🧪 **Testing Infrastructure**

### **Configuration Test**
**File**: `coupon-auth-service/benchmark/config_test.go`

```go
func TestConfigLoading(t *testing.T) {
    cfg, err := loadBenchmarkConfig()
    if err != nil {
        t.Fatalf("Failed to load benchmark config: %v", err)
    }
    
    // Verify config values are loaded correctly
    if cfg.Service.Port == 0 {
        t.Error("Service port not loaded")
    }
    // ... other validations
}
```

## 🚀 **Automation Scripts**

### **Benchmark Config Fix Script**
**File**: `scripts/fix-benchmark-config.sh`

- Automatically applies the same fix to all services
- Adds required imports (`viper`, `config`)
- Replaces `config.Load()` with `loadBenchmarkConfig()`
- Adds configuration loading functions
- Creates backups before making changes

### **Root Makefile Integration**
```bash
# Fix benchmark configuration across all services
make benchmark-fix-config
```

## 📊 **Configuration Search Priority**

The new benchmark config loader searches in this order:

1. **`../config/config.yaml`** - Service config directory (most common)
2. **`./config.yaml`** - Current benchmark directory
3. **`../config.yaml`** - Parent directory
4. **`../../config/config.yaml`** - Two levels up
5. **Fallback to `config.Load()`** - Standard search paths

## 🔧 **Usage Instructions**

### **For Auth Service (Already Fixed)**
```bash
cd coupon-auth-service
make benchmark  # Should now work without errors
```

### **For Other Services**
```bash
# Apply fix to all services
make benchmark-fix-config

# Then test individual services
cd coupon-user-service && make benchmark
cd coupon-voucher-service && make benchmark
# etc...
```

### **Manual Testing**
```bash
# Test just the config loading
cd coupon-auth-service/benchmark
go test -v -run TestConfigLoading
```

## 📁 **Files Modified**

### **Auth Service**
- `coupon-auth-service/benchmark/benchmark_test.go` - Added custom config loading
- `coupon-auth-service/benchmark/config_test.go` - Added config loading test

### **Automation**
- `scripts/fix-benchmark-config.sh` - Script to fix all services
- `Makefile` - Added `benchmark-fix-config` target

## 🎯 **Benefits of This Fix**

1. **✅ Eliminates .env File Dependency**: No need for `.env` files in benchmark directories
2. **✅ Correct Config Path Resolution**: Finds config files in proper service directories
3. **✅ Maintains Compatibility**: Falls back to standard config loading if needed
4. **✅ Consistent Defaults**: Uses same defaults as main service configuration
5. **✅ Automated Application**: Script can apply fix to all services
6. **✅ Testable**: Includes test to verify config loading works

## 🔍 **How It Works**

1. **Path Discovery**: Tries multiple relative paths to find config file
2. **File Existence Check**: Uses `os.Stat()` to verify file exists
3. **Custom Viper Instance**: Creates dedicated viper instance for config file
4. **Default Application**: Applies same defaults as shared config package
5. **Graceful Fallback**: Falls back to standard config loading if all else fails

## 🚀 **Next Steps**

1. **Apply to All Services**: Run `make benchmark-fix-config` to fix all services
2. **Test Benchmarks**: Verify `make benchmark` works in each service directory
3. **Run Full Benchmark Suite**: Use `make benchmark-all` to test complete system
4. **Monitor Results**: Check that benchmarks complete successfully

## ✨ **Summary**

This fix resolves the benchmark configuration loading issue by:
- **Creating custom config loading** that searches in correct relative paths
- **Eliminating dependency** on `.env` files in benchmark directories
- **Maintaining compatibility** with existing configuration structure
- **Providing automation** to apply fix across all services
- **Including tests** to verify configuration loading works correctly

The benchmark tests should now run successfully without configuration errors! 🎉
